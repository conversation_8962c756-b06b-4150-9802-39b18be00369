<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-924708"><g class="clips"><clipPath id="clip924708xyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clip924708x"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clip924708y"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clip924708xy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(285.31,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(456.12,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,506.84)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,445.89)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,384.95)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,324.01)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,263.07)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,202.13)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,141.18)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(114.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/><path class="yzl zl crisp" transform="translate(0,567.78)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clip924708xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace4a1d18" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,213.77L34.5,221.21" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,213.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,221.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceccf27c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,215.66L34.5,231.81" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,215.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,231.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb19166" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,181.53L34.5,173.61" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,181.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,173.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced6d229" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,55.93L34.5,118.28" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,55.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,118.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced2ed92" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,170.38L34.5,44.84" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,170.38)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,44.84)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9b31e9" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,124.92L34.5,63.49" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,124.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,63.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace50c3c5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,217L34.7,188.36" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,217)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,188.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1784c6" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,243.88L34.7,242.72" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,243.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,242.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2b5b19" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,247.96L34.7,168.25" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,247.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,168.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0edb16" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,143.57L34.7,47.46" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,143.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,47.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace78cb3b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,73.67L34.7,95.85" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,73.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,95.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0c2c8d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,139.67L34.7,115.23" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,139.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.7,115.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace7ab70c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,203.23L34.5,154.6" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,203.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,154.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6ca1e5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,239.92L34.5,229.56" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,239.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,229.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0608c2" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,179.16L34.5,190.86" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,179.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,190.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1a6b86" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,142.1L34.5,167.09" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,142.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,167.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace17d63e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,153.32L34.5,46.55" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,153.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,46.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace905249" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,101.88L34.5,51" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,101.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec938fe" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,213.95L34.5,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,213.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,359.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,430.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,451.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace123745" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,200.97L34.5,467.6" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,200.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,362.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,441.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0dcbd5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,219.01L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,219.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,395.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,441.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,456.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace66e1f0" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,66.78L34.5,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,66.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,360.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,422.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,447.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb5655a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,169.22L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,169.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,363.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,430.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,453.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,458.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace4acc99" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,64.16L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,64.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,348.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,423.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8bf3c1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,228.95L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,228.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,353.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,444.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceae16df" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,211.58L34.5,467.58L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,211.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,388.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,440.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace82396e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,198.29L34.5,467.62" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,198.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,380.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,448.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace3975e5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,155.45L34.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,155.45)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,313.38)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,414.7)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.73)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,458.61)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.72)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.02)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.55)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.2)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.91)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.84)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.14)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.39)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.34)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace335661" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,49.41L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,49.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,332.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,437.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,455.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceadcaad" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,139.18L34.5,467.61" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,139.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,365.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,428.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,454.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2e6095" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,152.59L41.88,361.49L51.1,442.85L64.02,453.41L80.62,459.05L100.91,464.66L124.89,464.96L152.56,466.29L183.92,466.86L506.75,467.58" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,152.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,361.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,442.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,453.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,459.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace5c7516" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,191.95L41.88,384.47L51.1,435.84L64.02,453.89L80.62,463.36L100.91,465.35L124.89,466.11L300.14,467.4L346.26,467.56L506.75,467.61" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,191.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,384.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,435.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,453.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,463.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,465.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,466.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace5bb00b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,237.66L41.88,374.78L51.1,444.21L64.02,459.78L80.62,462.28L100.91,464.23L124.89,465.56L183.92,466.75L218.97,467.15L506.75,467.58" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,237.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,374.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,444.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,459.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace672168" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,169.22L41.88,334.31L51.1,421.98L64.02,453.16L80.62,458.15L100.91,463.06L124.89,464.43L183.92,466.22L218.97,466.83L506.75,467.51" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,169.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,334.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,421.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,453.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,458.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9a7b33" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,150.51L41.88,334.63L51.1,427.68L64.02,451.26L80.62,458.62L100.91,463.9L124.89,465.4L183.92,466.69L218.97,466.54L300.14,467.33L346.26,467.25L506.75,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,150.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,334.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,427.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,451.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,458.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace35a339" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,31.25L41.88,382.13L51.1,434.37L64.02,455.47L80.62,459.48L100.91,464.41L124.89,464.99L152.56,466.14L183.92,466.68L257.71,466.84L300.14,467.16L506.75,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,382.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,434.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,455.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,459.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,466.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace85d170" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,246.19L34.5,467.55L34.5,467.51" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,246.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,353.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,434.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,454.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracea4c7db" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,154.6L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,154.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,377.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,441.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,454.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb33ac3" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,220.84L34.5,467.6" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,220.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,374.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,441.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace55cc1f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,38.08L34.5,467.54" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,38.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,337.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,422.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,445.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0d1ac5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,41.49L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,41.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,330.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,434.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,451.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceabc262" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,124.86L34.5,467.58" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,124.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,321.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,427.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,455.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceae7486" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,235.28L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,235.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,354.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,439.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,455.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracee0a9a1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,180.31L34.5,467.62" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,180.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,383.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,445.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9be35d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,192.99L34.5,467.61" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,192.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,391.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,443.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace86d9e3" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,101.21L34.5,467.43" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,101.21)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,306.92)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,420.35)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,451.83)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.6)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.65)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.22)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.31)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.24)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.57)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.98)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.92)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.32)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.37)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.43)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracece9afb" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,56.48L34.5,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,56.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,346.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,432.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,449.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceaa0653" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,80L34.5,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,80)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,369.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,438.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace49cbc2" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,195L41.88,352.3L51.1,442.42L64.02,451.74L80.62,462.17L100.91,463.95L124.89,464.91L152.56,466.26L183.92,466.92L257.71,467.11L300.14,467.24L506.75,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,195)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,352.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,442.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,451.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8dce5b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,186.29L41.88,382.26L51.1,441.8L64.02,456.78L80.62,461.41L100.91,464.81L124.89,465.52L152.56,466.61L183.92,466.56L300.14,467.46L346.26,467.5L506.75,467.62" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,186.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,382.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,441.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,456.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,461.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2d977f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,245.1L41.88,382.35L51.1,443.2L64.02,455.36L80.62,462.5L100.91,464.81L124.89,465.83L183.92,466.92L218.97,467.05L396.07,467.45L449.56,467.58L506.75,467.64" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,245.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,382.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,443.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,455.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2eea6d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,136.38L41.88,323.01L51.1,427.23L64.02,453.24L80.62,457.46L100.91,463.39L124.89,464.49L152.56,465.73L183.92,466.32L506.75,467.48" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,136.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,323.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,427.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,453.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,457.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace63e807" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,134.18L41.88,354.95L51.1,436.28L64.02,454.83L80.62,458.9L100.91,463.49L124.89,465.23L257.71,467.13L300.14,467.18L506.75,467.58" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,134.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,354.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,436.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,454.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,458.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9622d0" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,137.11L41.88,335.86L51.1,429.94L64.02,453.72L80.62,460.13L100.91,463.4L124.89,464.63L152.56,466.02L183.92,466.35L257.71,467.11L300.14,467.18L506.75,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,137.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,335.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,429.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,453.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,460.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,464.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="615" transform="translate(114.5,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(285.31,0)">5k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(456.12,0)">10k</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,567.78)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,506.84)">100</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,445.89)">200</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,384.95)">300</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,324.01)">400</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,263.07)">500</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,202.13)">600</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,141.18)">700</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-924708"><g class="clips"/><clipPath id="legend924708"><rect width="431" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(189,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="431" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legend924708)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Without surface</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="146.359375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(148.859375,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">No GridFF</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="107.640625" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(259,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">GridFF with BSpline</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="169.234375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="646.1" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Atoms</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,34.184375,350)" x="34.184375" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>