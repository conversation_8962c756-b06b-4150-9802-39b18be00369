<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-c3d92c"><g class="clips"><clipPath id="clipc3d92cxyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clipc3d92cx"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clipc3d92cy"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clipc3d92cxy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(285.31,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(456.12,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,506.99)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,446.22)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,385.45)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,324.68)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,263.90999999999997)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,203.14)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,142.37)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(114.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/><path class="yzl zl crisp" transform="translate(0,567.76)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clipc3d92cxyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace426541" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,154.31L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,154.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,377.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,435.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,456.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace27a3fc" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,149.14L34.5,467.62" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,149.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,373.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,443.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,456.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace23f82c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,238.78L34.5,467.63" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,238.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,381.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,436.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,457.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace279115" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,68.08L34.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,68.08)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,288.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,423.17)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,449.61)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.07)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.33)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.52)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.56)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.08)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.7)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.9)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.24)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.37)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.38)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace158cda" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,41.28L34.5,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,41.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,361.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,427.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,453.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9f6093" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,149.69L34.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,149.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,370.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,434.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,458.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace340781" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,194.96L34.5,467.58L34.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,194.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,377.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,438.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,454.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceac56ac" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,165.86L34.5,467.58L34.5,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,165.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,357.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,442.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,455.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace28400e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,148.11L34.5,467.62" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,148.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,366.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,441.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,455.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2ea383" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,157.17L34.5,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,157.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,326.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,417.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,451.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,459.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,462.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracecb9e96" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,68.08L34.5,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,68.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,337.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,431.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,452.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,460.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,463.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace796594" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,124.47L34.5,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,124.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,350.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,432.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,453.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,461.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,464.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,465.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(34.5,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9c7bdb" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,171.51L41.88,350.89L51.1,434.79L64.02,455.22L80.62,462.33L124.89,465.76L152.56,466.21L300.14,467.28L346.26,467.33L506.75,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,171.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,350.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,434.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,455.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace376893" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,234.22L41.88,399.54L51.1,446.98L64.02,455.52L80.62,462.8L124.89,465.91L152.56,466.18L218.97,467.03L257.71,467.25L506.75,467.63" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,234.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,399.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,446.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,455.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace667f5c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,244.19L41.88,396.26L51.1,446.67L64.02,456.25L80.62,463.47L100.91,465.46L124.89,465.59L152.56,466.53L183.92,466.81L346.26,467.5L396.07,467.53L506.75,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,244.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,396.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,446.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,456.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,463.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,465.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceda510d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,99.43L41.88,351.32L51.1,430.78L64.02,447.36L80.62,460.37L100.91,462.42L124.89,463.99L183.92,466.28L218.97,466.9L449.56,467.41L506.75,467.51" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,99.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,351.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,430.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,447.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,460.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,462.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,463.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,466.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracecd547c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,31.25L41.88,359.72L51.1,440.7L64.02,451.29L80.62,461.78L100.91,463.06L124.89,465.43L300.14,467.34L346.26,467.35L506.75,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,359.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,440.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,451.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,461.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace57bc58" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,32.53L41.88,375.31L51.1,433.36L64.02,454.68L80.62,457.64L100.91,464.03L124.89,465.06L218.97,466.78L257.71,466.97L396.07,467.31L449.56,467.51L506.75,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,32.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,375.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,433.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,454.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,457.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,466.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="615" transform="translate(114.5,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(285.31,0)">5k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(456.12,0)">10k</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,567.76)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,506.99)">100</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,446.22)">200</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,385.45)">300</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,324.68)">400</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,263.90999999999997)">500</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,203.14)">600</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,142.37)">700</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-c3d92c"><g class="clips"/><clipPath id="legendc3d92c"><rect width="172" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(448,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="172" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legendc3d92c)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">GridFF with BSpline</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="169.234375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="646.1" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Atoms</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,34.184375,350)" x="34.184375" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>