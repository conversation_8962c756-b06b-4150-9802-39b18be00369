<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-e8f1ca"><g class="clips"><clipPath id="clipe8f1caxyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clipe8f1cax"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clipe8f1cay"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clipe8f1caxy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(283.68,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(454.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,462.44)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,350.28)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,238.12)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,125.95)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(112.65,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/><path class="yzl zl crisp" transform="translate(0,574.61)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clipe8f1caxyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace3169ca" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,221.71L40.04,306.37L49.28,368.87L62.21,405.34L78.83,423.08L99.15,437.67L123.16,445.71L150.86,451.92L182.26,456.47L217.36,459.63L256.15,462.13L344.81,465.51L394.68,466.74L505.5,468.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,221.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,306.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,368.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,405.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,423.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,437.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,445.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,451.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,456.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,459.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,462.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,464.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,465.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,466.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,467.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace86b0c4" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,159.92L40.04,275.02L49.28,354.46L62.21,395.42L78.83,421.39L99.15,434.76L123.16,444.57L150.86,451.25L182.26,455.92L217.36,459.32L256.15,461.88L344.81,465.4L394.68,466.65L505.5,468.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,159.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,275.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,354.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,395.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,421.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,434.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,444.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,451.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,455.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,459.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,461.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,463.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,466.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,467.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2a1245" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,156.45L40.04,273.85L49.28,351.59L62.21,394.6L78.83,419.56L99.15,434.75L123.16,444.6L150.86,451.19L182.26,455.93L217.36,459.33L256.15,461.89L344.81,465.6L394.68,466.65L505.5,468.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,156.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,273.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,351.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,394.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,419.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,434.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,444.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,451.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,455.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,459.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,461.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,463.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,465.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,466.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,467.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6c418a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,108.03L40.04,244.82L49.28,335.55L62.21,385.06L78.83,411.66L99.15,428.39L123.16,441.14L150.86,447.75L182.26,453.42L217.36,457.41L256.15,460.1L344.81,463.95L394.68,465.28L505.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,108.03)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,244.82)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,335.55)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,385.06)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,411.66)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,428.39)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,441.14)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,447.75)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,453.42)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,457.41)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,460.1)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,462.12)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,463.95)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,465.28)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,466.46)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace260584" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,31.25L40.04,214.14L49.28,321.69L62.21,376.89L78.83,408.71L99.15,427.21L123.16,439.14L150.86,447.01L182.26,452.62L217.36,456.64L256.15,459.63L344.81,463.77L394.68,465.28L505.5,467.63" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,214.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,321.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,376.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,408.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,427.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,439.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,447.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,452.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,456.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,459.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,461.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,463.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,465.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1d531f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,46.24L40.04,218.22L49.28,327.08L62.21,379.34L78.83,409.01L99.15,427.62L123.16,439.16L150.86,447.52L182.26,452.75L217.36,456.89L256.15,459.61L344.81,464.99L394.68,466.32L505.5,468.19" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,46.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,218.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,327.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,379.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,409.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,427.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,439.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,447.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,452.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,456.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,459.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,462.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,464.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,466.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2d9b75" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,264.55L40.04,266.04L49.28,296.81L62.21,319.96L78.83,327.21L99.15,322.55L123.16,343.25L150.86,348.31L182.26,337.71L217.36,340.75L256.15,355.05L298.63,351.76L344.81,338.67L394.68,350.49L448.24,357.96L505.5,351.86" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,264.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,266.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,296.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,319.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,327.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,322.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,343.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,348.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,337.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,340.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,355.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,351.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,338.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,350.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,357.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,351.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace317cac" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,186.69L40.04,196.86L49.28,239.71L62.21,269.98L78.83,306.82L99.15,309.68L123.16,303.52L150.86,314.81L182.26,312.7L217.36,316.69L256.15,325.44L298.63,318.19L344.81,323.77L394.68,326.97L448.24,331.28L505.5,324.73" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,186.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,196.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,239.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,269.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,306.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,309.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,303.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,314.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,312.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,316.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,325.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,318.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,323.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,326.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,331.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,324.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8991bb" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,197.33L40.04,183.61L49.28,235.98L62.21,270.64L78.83,285.94L99.15,297.88L123.16,300.72L150.86,306.62L182.26,310.15L217.36,319.48L256.15,336.91L298.63,314.56L344.81,319.53L448.24,326.37L505.5,322.2" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,197.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,183.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,235.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,270.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,285.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,297.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,300.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,306.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,310.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,319.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,336.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,314.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,319.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,323.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,326.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,322.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6d96ab" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,161.36L40.04,220.05L49.28,222.46L62.21,242.93L78.83,264.21L99.15,278.39L123.16,290.9L150.86,299.74L182.26,320.96L217.36,306.31L256.15,326.53L298.63,308.76L344.81,326.33L394.68,318.91L448.24,319.61L505.5,322.23" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,161.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,220.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,222.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,242.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,264.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,278.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,290.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,299.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,320.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,306.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,326.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,308.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,326.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,318.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,319.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,322.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced6a1dd" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,109.82L40.04,192.51L49.28,194.13L62.21,219.35L78.83,258.58L99.15,266.81L123.16,271.95L150.86,280.19L182.26,283.89L217.36,288.29L256.15,289.23L344.81,296.43L394.68,297.66L448.24,303.41L505.5,306.08" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,109.82)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,192.51)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,194.13)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,219.35)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,258.58)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,266.81)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,271.95)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,280.19)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,283.89)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,288.29)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,289.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,292.91)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,296.43)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,297.66)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,303.41)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,306.08)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceefe881" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,123.36L40.04,160.82L49.28,195.82L62.21,215.87L78.83,246.92L99.15,265.13L123.16,275.85L150.86,284.11L182.26,287.17L217.36,289.97L256.15,294.6L298.63,293.84L344.81,310.17L394.68,319.35L448.24,300.38L505.5,315.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,123.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(40.04,160.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(49.28,195.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(62.21,215.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(78.83,246.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(99.15,265.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(123.16,275.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(150.86,284.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(182.26,287.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(217.36,289.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(256.15,294.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(298.63,293.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(344.81,310.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(394.68,319.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(448.24,300.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,315.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="615" transform="translate(112.65,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(283.68,0)">5k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(454.7,0)">10k</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,574.61)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,462.44)">5M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,350.28)">10M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,238.12)">15M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,125.95)">20M</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-e8f1ca"><g class="clips"/><clipPath id="legende8f1ca"><rect width="282" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(338,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="282" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legende8f1ca)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">No GridFF</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="107.640625" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(110.140625,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">GridFF with BSpline</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="169.234375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="646.1" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Atoms</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,29.543750000000003,350)" x="29.543750000000003" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>