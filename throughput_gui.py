#!/usr/bin/env python3
"""
GUI interface for Throughput MD simulation parameters
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import threading

class ThroughputGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Throughput MD Simulation GUI")
        self.root.geometry("800x900")
        
        # Create main frame with scrollbar
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Basic Parameters Tab
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="Basic Parameters")
        self.create_basic_params(basic_frame)
        
        # Advanced Parameters Tab
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="Advanced Parameters")
        self.create_advanced_params(advanced_frame)
        
        # Local Memory Parameters Tab
        memory_frame = ttk.Frame(notebook)
        notebook.add(memory_frame, text="Local Memory")
        self.create_memory_params(memory_frame)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(control_frame, text="Run Simulation", command=self.run_simulation).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Save Parameters", command=self.save_parameters).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Load Parameters", command=self.load_parameters).pack(side=tk.LEFT)
        
        # Output text area
        output_frame = ttk.LabelFrame(main_frame, text="Output")
        output_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.output_text = tk.Text(output_frame, height=10)
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        self.output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_basic_params(self, parent):
        # Basic simulation parameters
        ttk.Label(parent, text="Basic Simulation Parameters", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
        
        # Force field flags
        flags_frame = ttk.LabelFrame(parent, text="Force Field Flags")
        flags_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.dovdW = tk.IntVar(value=1)
        ttk.Checkbutton(flags_frame, text="Van der Waals (dovdW)", variable=self.dovdW).pack(anchor=tk.W)
        
        self.doSurfAtoms = tk.IntVar(value=1)
        ttk.Checkbutton(flags_frame, text="Surface Atoms (doSurfAtoms)", variable=self.doSurfAtoms).pack(anchor=tk.W)
        
        # Grid FF selection
        grid_frame = ttk.Frame(flags_frame)
        grid_frame.pack(fill=tk.X, pady=5)
        ttk.Label(grid_frame, text="Grid FF:").pack(side=tk.LEFT)
        self.bGridFF = tk.IntVar(value=6)
        ttk.Radiobutton(grid_frame, text="Linear (1)", variable=self.bGridFF, value=1).pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(grid_frame, text="bSpline (6)", variable=self.bGridFF, value=6).pack(side=tk.LEFT, padx=10)
        
        self.bTex = tk.IntVar(value=0)
        ttk.Checkbutton(flags_frame, text="Texture (bTex)", variable=self.bTex).pack(anchor=tk.W)
        
        # File paths
        files_frame = ttk.LabelFrame(parent, text="File Paths")
        files_frame.pack(fill=tk.X, pady=(0, 10))
        
        # XYZ file
        xyz_frame = ttk.Frame(files_frame)
        xyz_frame.pack(fill=tk.X, pady=2)
        ttk.Label(xyz_frame, text="XYZ File:").pack(side=tk.LEFT)
        self.xyz_name = tk.StringVar(value="data/xyz/xylitol_WO_gridFF")
        ttk.Entry(xyz_frame, textvariable=self.xyz_name, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(xyz_frame, text="Browse", command=self.browse_xyz).pack(side=tk.RIGHT)
        
        # Convergence criteria
        conv_frame = ttk.LabelFrame(parent, text="Convergence")
        conv_frame.pack(fill=tk.X, pady=(0, 10))
        
        fconv_frame = ttk.Frame(conv_frame)
        fconv_frame.pack(fill=tk.X)
        ttk.Label(fconv_frame, text="Force Convergence:").pack(side=tk.LEFT)
        self.Fconv = tk.StringVar(value="1e-4")
        ttk.Entry(fconv_frame, textvariable=self.Fconv, width=15).pack(side=tk.LEFT, padx=5)
        
    def create_advanced_params(self, parent):
        ttk.Label(parent, text="Advanced Parameters", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
        
        # Simulation parameters arrays
        sim_frame = ttk.LabelFrame(parent, text="Simulation Parameters")
        sim_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Replicas
        rep_frame = ttk.Frame(sim_frame)
        rep_frame.pack(fill=tk.X, pady=2)
        ttk.Label(rep_frame, text="Replicas:").pack(side=tk.LEFT)
        self.replicas = tk.StringVar(value="1000,5000")
        ttk.Entry(rep_frame, textvariable=self.replicas, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Label(rep_frame, text="(comma-separated)").pack(side=tk.LEFT)
        
        # Per frames
        pf_frame = ttk.Frame(sim_frame)
        pf_frame.pack(fill=tk.X, pady=2)
        ttk.Label(pf_frame, text="Per Frames:").pack(side=tk.LEFT)
        self.perframes = tk.StringVar(value="20,500")
        ttk.Entry(pf_frame, textvariable=self.perframes, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Label(pf_frame, text="(comma-separated)").pack(side=tk.LEFT)
        
        # Per VF
        pvf_frame = ttk.Frame(sim_frame)
        pvf_frame.pack(fill=tk.X, pady=2)
        ttk.Label(pvf_frame, text="Per VF:").pack(side=tk.LEFT)
        self.perVF = tk.StringVar(value="20,50")
        ttk.Entry(pvf_frame, textvariable=self.perVF, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Label(pvf_frame, text="(comma-separated)").pack(side=tk.LEFT)
        
        # PBC parameters
        pbc_frame = ttk.LabelFrame(parent, text="PBC Parameters")
        pbc_frame.pack(fill=tk.X, pady=(0, 10))
        
        npbc_frame = ttk.Frame(pbc_frame)
        npbc_frame.pack(fill=tk.X, pady=2)
        ttk.Label(npbc_frame, text="nPBC:").pack(side=tk.LEFT)
        self.nPBC = tk.StringVar(value="(1,1,0)")
        ttk.Entry(npbc_frame, textvariable=self.nPBC, width=30).pack(side=tk.LEFT, padx=5)
        
        # Surface parameters
        surf_frame = ttk.LabelFrame(parent, text="Surface Parameters")
        surf_frame.pack(fill=tk.X, pady=(0, 10))
        
        ns_frame = ttk.Frame(surf_frame)
        ns_frame.pack(fill=tk.X, pady=2)
        ttk.Label(ns_frame, text="Surface Sizes (N):").pack(side=tk.LEFT)
        self.Ns = tk.StringVar(value="16")
        ttk.Entry(ns_frame, textvariable=self.Ns, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Label(ns_frame, text="(comma-separated)").pack(side=tk.LEFT)
        
    def create_memory_params(self, parent):
        ttk.Label(parent, text="Local Memory Parameters", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
        
        mem_frame = ttk.LabelFrame(parent, text="Local Memory Settings")
        mem_frame.pack(fill=tk.X, pady=(0, 10))
        
        # nlocMMFFs
        mmff_frame = ttk.Frame(mem_frame)
        mmff_frame.pack(fill=tk.X, pady=2)
        ttk.Label(mmff_frame, text="nlocMMFFs:").pack(side=tk.LEFT)
        self.nlocMMFFs = tk.StringVar(value="32")
        ttk.Entry(mmff_frame, textvariable=self.nlocMMFFs, width=30).pack(side=tk.LEFT, padx=5)
        
        # nlocmoves
        moves_frame = ttk.Frame(mem_frame)
        moves_frame.pack(fill=tk.X, pady=2)
        ttk.Label(moves_frame, text="nlocmoves:").pack(side=tk.LEFT)
        self.nlocmoves = tk.StringVar(value="32")
        ttk.Entry(moves_frame, textvariable=self.nlocmoves, width=30).pack(side=tk.LEFT, padx=5)
        
        # nlocNBFFs
        nbff_frame = ttk.Frame(mem_frame)
        nbff_frame.pack(fill=tk.X, pady=2)
        ttk.Label(nbff_frame, text="nlocNBFFs:").pack(side=tk.LEFT)
        self.nlocNBFFs = tk.StringVar(value="--")
        ttk.Entry(nbff_frame, textvariable=self.nlocNBFFs, width=30).pack(side=tk.LEFT, padx=5)
        
        # nlocSurfs
        surfs_frame = ttk.Frame(mem_frame)
        surfs_frame.pack(fill=tk.X, pady=2)
        ttk.Label(surfs_frame, text="nlocSurfs:").pack(side=tk.LEFT)
        self.nlocSurfs = tk.StringVar(value="--")
        ttk.Entry(surfs_frame, textvariable=self.nlocSurfs, width=30).pack(side=tk.LEFT, padx=5)
        
        # nlocGridFFs
        gridff_frame = ttk.Frame(mem_frame)
        gridff_frame.pack(fill=tk.X, pady=2)
        ttk.Label(gridff_frame, text="nlocGridFFs:").pack(side=tk.LEFT)
        self.nlocGridFFs = tk.StringVar(value="--")
        ttk.Entry(gridff_frame, textvariable=self.nlocGridFFs, width=30).pack(side=tk.LEFT, padx=5)
        
        # nlocGridFFbSplines
        spline_frame = ttk.Frame(mem_frame)
        spline_frame.pack(fill=tk.X, pady=2)
        ttk.Label(spline_frame, text="nlocGridFFbSplines:").pack(side=tk.LEFT)
        self.nlocGridFFbSplines = tk.StringVar(value="--")
        ttk.Entry(spline_frame, textvariable=self.nlocGridFFbSplines, width=30).pack(side=tk.LEFT, padx=5)
        
    def browse_xyz(self):
        filename = filedialog.askopenfilename(
            title="Select XYZ file",
            filetypes=[("XYZ files", "*.xyz"), ("All files", "*.*")]
        )
        if filename:
            self.xyz_name.set(filename)
    
    def run_simulation(self):
        """Run the simulation in a separate thread"""
        def run_thread():
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, "Starting simulation...\n")
            self.output_text.update()
            
            try:
                # Test with a simple parameter set first
                cmd = [
                    "python3", "run_throughput_MD.py",
                    "--nSys", "1000",
                    "--xyz_name", self.xyz_name.get(),
                    "--dovdW", str(self.dovdW.get()),
                    "--doSurfAtoms", str(self.doSurfAtoms.get()),
                    "--GridFF", str(self.bGridFF.get()),
                    "--Fconv", self.Fconv.get(),
                    "--perframe", "20",
                    "--perVF", "20",
                    "--gridnPBC", self.nPBC.get()
                ]
                
                if self.doSurfAtoms.get():
                    cmd.extend(["--surf_name", "data/xyz/surfaces_for_throughput/NaCl_16x16_L3"])
                
                self.output_text.insert(tk.END, f"Command: {' '.join(cmd)}\n\n")
                self.output_text.update()
                
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                
                self.output_text.insert(tk.END, "STDOUT:\n")
                self.output_text.insert(tk.END, result.stdout)
                self.output_text.insert(tk.END, "\nSTDERR:\n")
                self.output_text.insert(tk.END, result.stderr)
                self.output_text.insert(tk.END, f"\nReturn code: {result.returncode}\n")
                
            except Exception as e:
                self.output_text.insert(tk.END, f"Error: {str(e)}\n")
            
            self.output_text.see(tk.END)
        
        threading.Thread(target=run_thread, daemon=True).start()
    
    def save_parameters(self):
        """Save current parameters to a file"""
        filename = filedialog.asksaveasfilename(
            title="Save parameters",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(f"dovdW={self.dovdW.get()}\n")
                    f.write(f"doSurfAtoms={self.doSurfAtoms.get()}\n")
                    f.write(f"bGridFF={self.bGridFF.get()}\n")
                    f.write(f"bTex={self.bTex.get()}\n")
                    f.write(f"xyz_name={self.xyz_name.get()}\n")
                    f.write(f"Fconv={self.Fconv.get()}\n")
                    f.write(f"replicas={self.replicas.get()}\n")
                    f.write(f"perframes={self.perframes.get()}\n")
                    f.write(f"perVF={self.perVF.get()}\n")
                    f.write(f"nPBC={self.nPBC.get()}\n")
                    f.write(f"Ns={self.Ns.get()}\n")
                    f.write(f"nlocMMFFs={self.nlocMMFFs.get()}\n")
                    f.write(f"nlocmoves={self.nlocmoves.get()}\n")
                    f.write(f"nlocNBFFs={self.nlocNBFFs.get()}\n")
                    f.write(f"nlocSurfs={self.nlocSurfs.get()}\n")
                    f.write(f"nlocGridFFs={self.nlocGridFFs.get()}\n")
                    f.write(f"nlocGridFFbSplines={self.nlocGridFFbSplines.get()}\n")
                messagebox.showinfo("Success", "Parameters saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save parameters: {str(e)}")
    
    def load_parameters(self):
        """Load parameters from a file"""
        filename = filedialog.askopenfilename(
            title="Load parameters",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    for line in f:
                        if '=' in line:
                            key, value = line.strip().split('=', 1)
                            if hasattr(self, key):
                                var = getattr(self, key)
                                if isinstance(var, tk.IntVar):
                                    var.set(int(value))
                                elif isinstance(var, tk.StringVar):
                                    var.set(value)
                messagebox.showinfo("Success", "Parameters loaded successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load parameters: {str(e)}")

def main():
    root = tk.Tk()
    app = ThroughputGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
