<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-0f65b1"><g class="clips"><clipPath id="clip0f65b1xyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clip0f65b1x"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clip0f65b1y"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clip0f65b1xy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(114.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(145.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(177.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(208.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(240.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(271.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(302.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(334.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(365.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(397.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(428.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(459.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(491.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(522.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(554.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(585.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,462.44)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,350.28)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,238.12)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,125.95)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,574.61)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clip0f65b1xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace6edf8d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,221.71L65.9,306.37L97.3,368.87L128.7,405.34L160.1,423.08L191.5,437.67L222.9,445.71L254.3,451.92L285.7,456.47L348.5,462.13L379.9,464.02L442.7,466.74L474.1,467.72L505.5,468.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,221.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,306.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,368.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,405.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,423.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,437.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,445.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,451.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,456.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,459.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,462.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,464.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,465.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,466.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8445ba" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,159.92L65.9,275.02L97.3,354.46L128.7,395.42L160.1,421.39L191.5,434.76L222.9,444.57L254.3,451.25L285.7,455.92L317.1,459.32L348.5,461.88L411.3,465.4L442.7,466.65L505.5,468.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,159.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,275.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,354.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,395.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,421.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,434.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,444.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,451.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,455.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,459.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,461.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,463.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,466.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace68edea" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,156.45L65.9,273.85L97.3,351.59L128.7,394.6L160.1,419.56L191.5,434.75L222.9,444.6L254.3,451.19L285.7,455.93L317.1,459.33L348.5,461.89L411.3,465.6L442.7,466.65L505.5,468.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,156.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,273.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,351.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,394.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,419.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,434.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,444.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,451.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,455.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,459.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,461.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,463.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,465.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,466.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1deecd" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,108.03L65.9,244.82L97.3,335.55L128.7,385.06L160.1,411.66L191.5,428.39L222.9,441.14L254.3,447.75L285.7,453.42L317.1,457.41L348.5,460.1L411.3,463.95L442.7,465.28L505.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,108.03)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,244.82)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,335.55)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,385.06)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,411.66)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,428.39)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,441.14)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,447.75)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,453.42)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,457.41)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,460.1)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,462.12)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,463.95)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,465.28)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,466.46)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace08d569" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,31.25L65.9,214.14L97.3,321.69L128.7,376.89L160.1,408.71L191.5,427.21L222.9,439.14L254.3,447.01L285.7,452.62L317.1,456.64L348.5,459.63L411.3,463.77L442.7,465.28L505.5,467.63" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,214.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,321.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,376.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,408.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,427.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,439.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,447.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,452.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,456.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,459.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,461.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,463.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,465.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace29c0a4" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,46.24L65.9,218.22L97.3,327.08L128.7,379.34L160.1,409.01L191.5,427.62L222.9,439.16L254.3,447.52L285.7,452.75L317.1,456.89L348.5,459.61L411.3,464.99L442.7,466.32L505.5,468.19" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,46.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,218.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,327.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,379.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,409.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,427.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,439.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,447.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,452.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,456.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,459.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,462.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,464.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,466.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,468.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace947e48" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,264.55L65.9,266.04L97.3,296.81L128.7,319.96L160.1,327.21L191.5,322.55L222.9,343.25L254.3,348.31L285.7,337.71L317.1,340.75L348.5,355.05L379.9,351.76L411.3,338.67L442.7,350.49L474.1,357.96L505.5,351.86" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,264.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,266.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,296.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,319.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,327.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,322.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,343.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,348.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,337.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,340.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,355.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,351.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,338.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,350.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,357.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,351.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb77f3e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,186.69L65.9,196.86L97.3,239.71L128.7,269.98L160.1,306.82L191.5,309.68L222.9,303.52L254.3,314.81L285.7,312.7L317.1,316.69L348.5,325.44L379.9,318.19L411.3,323.77L442.7,326.97L474.1,331.28L505.5,324.73" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,186.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,196.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,239.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,269.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,306.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,309.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,303.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,314.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,312.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,316.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,325.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,318.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,323.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,326.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,331.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,324.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace980c72" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,197.33L65.9,183.61L97.3,235.98L128.7,270.64L160.1,285.94L191.5,297.88L222.9,300.72L254.3,306.62L285.7,310.15L317.1,319.48L348.5,336.91L379.9,314.56L411.3,319.53L474.1,326.37L505.5,322.2" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,197.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,183.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,235.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,270.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,285.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,297.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,300.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,306.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,310.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,319.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,336.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,314.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,319.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,323.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,326.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,322.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6adc76" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,161.36L65.9,220.05L97.3,222.46L160.1,264.21L191.5,278.39L222.9,290.9L254.3,299.74L285.7,320.96L317.1,306.31L348.5,326.53L379.9,308.76L411.3,326.33L442.7,318.91L474.1,319.61L505.5,322.23" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,161.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,220.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,222.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,242.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,264.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,278.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,290.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,299.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,320.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,306.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,326.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,308.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,326.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,318.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,319.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,322.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace22b8c1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,109.82L65.9,192.51L97.3,194.13L128.7,219.35L160.1,258.58L191.5,266.81L222.9,271.95L254.3,280.19L285.7,283.89L317.1,288.29L348.5,289.23L411.3,296.43L442.7,297.66L474.1,303.41L505.5,306.08" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,109.82)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,192.51)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,194.13)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,219.35)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,258.58)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,266.81)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,271.95)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,280.19)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,283.89)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,288.29)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,289.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,292.91)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,296.43)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,297.66)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,303.41)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,306.08)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace55e6c3" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,123.36L65.9,160.82L97.3,195.82L128.7,215.87L160.1,246.92L191.5,265.13L222.9,275.85L254.3,284.11L285.7,287.17L317.1,289.97L348.5,294.6L379.9,293.84L411.3,310.17L442.7,319.35L474.1,300.38L505.5,315.55" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,123.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,160.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,195.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,215.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,246.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,265.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,275.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,284.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,287.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,289.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,294.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,293.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,310.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,319.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,300.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,315.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(114.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1x1</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(145.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">2x2</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(177.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">3x3</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(208.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">4x4</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(240.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">5x5</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(271.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">6x6</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(302.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">7x7</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(334.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">8x8</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(365.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">9x9</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(397.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">10x10</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(428.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">11x11</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(459.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">12x12</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(491.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">13x13</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(522.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">14x14</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(554.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">15x15</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(585.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">16x16</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,574.61)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,462.44)">5M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,350.28)">10M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,238.12)">15M</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,125.95)">20M</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-0f65b1"><g class="clips"/><clipPath id="legend0f65b1"><rect width="282" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(338,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="282" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legend0f65b1)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">No GridFF</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="107.640625" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(110.140625,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">GridFF with BSpline</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="169.234375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="664.3060546875" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Size</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,29.543750000000003,350)" x="29.543750000000003" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>